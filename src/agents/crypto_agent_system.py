#!/usr/bin/env python3
"""
QuantCrypto Multi-Agent System using LangGraph
Core agent orchestration for crypto analytics and rug detection
"""

import os
import asyncio
import json
from typing import Dict, Any, List, Optional, TypedDict
from datetime import datetime
import logging

from langchain_ollama import OllamaLLM
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
from langgraph.checkpoint.memory import MemorySaver

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentState(TypedDict):
    """State shared between agents"""
    messages: List[Any]
    current_task: str
    market_data: Dict[str, Any]
    analysis_results: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    next_agent: str
    iteration_count: int

class CryptoAgentSystem:
    """Multi-agent system for crypto analytics"""
    
    def __init__(self):
        self.llm = OllamaLLM(
            model="deepseek-r1:latest",
            base_url="http://localhost:11434",
            temperature=0.1,
            num_predict=512
        )
        
        # Initialize memory for state persistence
        self.memory = MemorySaver()
        
        # Agent prompts
        self.agent_prompts = {
            "data_ingestion": """You are a Data Ingestion Agent for a crypto analytics platform.
Your role is to collect, validate, and normalize cryptocurrency market data.

Current task: {task}
Available data sources: CoinGecko, Etherscan, Infura, Web3 Provider

Analyze the incoming data and provide:
1. Data quality assessment
2. Key metrics extracted
3. Anomalies detected
4. Recommendations for next analysis step

Be concise and focus on actionable insights.""",

            "rug_detection": """You are a Rug Pull Detection Agent with PhD-level expertise in blockchain analysis.
Your role is to identify potential rug pulls and malicious token activities.

Current market data: {market_data}
Analysis context: {context}

Perform comprehensive rug pull analysis:
1. Smart contract code analysis (if available)
2. Liquidity pool analysis
3. Trading pattern analysis
4. Wallet clustering analysis
5. Risk score calculation (0-100)

Provide detailed risk assessment with specific evidence.""",

            "forecasting": """You are a Quantitative Forecasting Agent with Renaissance Technologies-level expertise.
Your role is to generate accurate price predictions and market forecasts.

Market data: {market_data}
Risk assessment: {risk_assessment}

Generate forecasting analysis:
1. Technical indicator analysis
2. Market sentiment analysis
3. Price prediction (1D, 7D, 30D)
4. Confidence intervals
5. Key risk factors

Use quantitative methods and provide probabilistic forecasts.""",

            "verification": """You are a Verification Agent responsible for cross-validating analysis results.
Your role is to ensure accuracy and consensus across agent findings.

Analysis results: {analysis_results}
Risk assessment: {risk_assessment}

Perform verification:
1. Cross-check findings for consistency
2. Identify conflicting assessments
3. Calculate confidence scores
4. Recommend final actions
5. Flag any uncertainties

Provide final consensus with confidence levels."""
        }
        
        # Build the agent graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow"""
        
        # Define the graph
        workflow = StateGraph(AgentState)
        
        # Add agent nodes
        workflow.add_node("data_ingestion", self._data_ingestion_agent)
        workflow.add_node("rug_detection", self._rug_detection_agent)
        workflow.add_node("forecasting", self._forecasting_agent)
        workflow.add_node("verification", self._verification_agent)
        workflow.add_node("coordinator", self._coordinator_agent)
        
        # Define the workflow edges
        workflow.set_entry_point("coordinator")
        
        # Coordinator decides which agent to call first
        workflow.add_conditional_edges(
            "coordinator",
            self._route_to_agent,
            {
                "data_ingestion": "data_ingestion",
                "rug_detection": "rug_detection", 
                "forecasting": "forecasting",
                "verification": "verification",
                "end": END
            }
        )
        
        # Data ingestion flows to rug detection
        workflow.add_edge("data_ingestion", "rug_detection")
        
        # Rug detection flows to forecasting
        workflow.add_edge("rug_detection", "forecasting")
        
        # Forecasting flows to verification
        workflow.add_edge("forecasting", "verification")
        
        # Verification flows back to coordinator for final decision
        workflow.add_edge("verification", "coordinator")
        
        # Compile the graph
        return workflow.compile(checkpointer=self.memory)
    
    def _route_to_agent(self, state: AgentState) -> str:
        """Route to appropriate agent based on state"""
        if state["iteration_count"] == 0:
            return "data_ingestion"
        elif state["iteration_count"] >= 4:
            return "end"
        else:
            return state.get("next_agent", "end")
    
    async def _data_ingestion_agent(self, state: AgentState) -> AgentState:
        """Data ingestion and validation agent"""
        logger.info("🔄 Data Ingestion Agent activated")
        
        prompt = ChatPromptTemplate.from_template(self.agent_prompts["data_ingestion"])
        
        try:
            # Simulate data ingestion (in real implementation, this would call APIs)
            market_data = {
                "bitcoin": {"price": 109115, "change_24h": 2.3, "volume": 28500000000},
                "ethereum": {"price": 2568.13, "change_24h": 1.8, "volume": 15200000000},
                "timestamp": datetime.now().isoformat(),
                "data_quality": "high",
                "sources": ["coingecko", "etherscan", "infura"]
            }
            
            # Get LLM analysis
            response = await self.llm.ainvoke(
                prompt.format(task=state["current_task"])
            )
            
            # Update state
            state["market_data"] = market_data
            state["messages"].append(AIMessage(content=f"Data Ingestion: {response}"))
            state["next_agent"] = "rug_detection"
            state["iteration_count"] += 1
            
            logger.info("✅ Data ingestion completed")
            
        except Exception as e:
            logger.error(f"❌ Data ingestion failed: {e}")
            state["messages"].append(AIMessage(content=f"Data ingestion error: {e}"))
        
        return state
    
    async def _rug_detection_agent(self, state: AgentState) -> AgentState:
        """Rug pull detection and analysis agent"""
        logger.info("🔍 Rug Detection Agent activated")
        
        prompt = ChatPromptTemplate.from_template(self.agent_prompts["rug_detection"])
        
        try:
            # Get LLM analysis
            response = await self.llm.ainvoke(
                prompt.format(
                    market_data=json.dumps(state["market_data"], indent=2),
                    context=state["current_task"]
                )
            )
            
            # Simulate risk assessment
            risk_assessment = {
                "overall_risk_score": 25,  # 0-100 scale
                "liquidity_risk": "low",
                "contract_risk": "medium", 
                "trading_pattern_risk": "low",
                "confidence": 0.85,
                "timestamp": datetime.now().isoformat()
            }
            
            # Update state
            state["risk_assessment"] = risk_assessment
            state["messages"].append(AIMessage(content=f"Rug Detection: {response}"))
            state["next_agent"] = "forecasting"
            state["iteration_count"] += 1
            
            logger.info("✅ Rug detection analysis completed")
            
        except Exception as e:
            logger.error(f"❌ Rug detection failed: {e}")
            state["messages"].append(AIMessage(content=f"Rug detection error: {e}"))
        
        return state
    
    async def _forecasting_agent(self, state: AgentState) -> AgentState:
        """Quantitative forecasting agent"""
        logger.info("📈 Forecasting Agent activated")
        
        prompt = ChatPromptTemplate.from_template(self.agent_prompts["forecasting"])
        
        try:
            # Get LLM analysis
            response = await self.llm.ainvoke(
                prompt.format(
                    market_data=json.dumps(state["market_data"], indent=2),
                    risk_assessment=json.dumps(state["risk_assessment"], indent=2)
                )
            )
            
            # Simulate forecasting results
            forecasting_results = {
                "btc_forecast_7d": {"price": 115000, "confidence": 0.72, "direction": "bullish"},
                "eth_forecast_7d": {"price": 2750, "confidence": 0.68, "direction": "bullish"},
                "market_sentiment": "cautiously_optimistic",
                "key_factors": ["institutional_adoption", "regulatory_clarity"],
                "timestamp": datetime.now().isoformat()
            }
            
            # Update state
            if "analysis_results" not in state:
                state["analysis_results"] = {}
            state["analysis_results"]["forecasting"] = forecasting_results
            state["messages"].append(AIMessage(content=f"Forecasting: {response}"))
            state["next_agent"] = "verification"
            state["iteration_count"] += 1
            
            logger.info("✅ Forecasting analysis completed")
            
        except Exception as e:
            logger.error(f"❌ Forecasting failed: {e}")
            state["messages"].append(AIMessage(content=f"Forecasting error: {e}"))
        
        return state
    
    async def _verification_agent(self, state: AgentState) -> AgentState:
        """Cross-validation and verification agent"""
        logger.info("✅ Verification Agent activated")
        
        prompt = ChatPromptTemplate.from_template(self.agent_prompts["verification"])
        
        try:
            # Get LLM analysis
            response = await self.llm.ainvoke(
                prompt.format(
                    analysis_results=json.dumps(state.get("analysis_results", {}), indent=2),
                    risk_assessment=json.dumps(state["risk_assessment"], indent=2)
                )
            )
            
            # Simulate verification results
            verification_results = {
                "consensus_score": 0.88,
                "conflicting_signals": [],
                "confidence_level": "high",
                "recommended_action": "proceed_with_caution",
                "final_assessment": "Analysis shows low-medium risk with positive outlook",
                "timestamp": datetime.now().isoformat()
            }
            
            # Update state
            state["analysis_results"]["verification"] = verification_results
            state["messages"].append(AIMessage(content=f"Verification: {response}"))
            state["next_agent"] = "end"
            state["iteration_count"] += 1
            
            logger.info("✅ Verification completed")
            
        except Exception as e:
            logger.error(f"❌ Verification failed: {e}")
            state["messages"].append(AIMessage(content=f"Verification error: {e}"))
        
        return state
    
    async def _coordinator_agent(self, state: AgentState) -> AgentState:
        """Coordinator agent for workflow management"""
        logger.info("🎯 Coordinator Agent activated")
        
        if state["iteration_count"] == 0:
            logger.info("🚀 Starting crypto analysis workflow")
            state["messages"].append(
                SystemMessage(content=f"Starting analysis for: {state['current_task']}")
            )
        else:
            logger.info("📊 Analysis workflow completed")
            
            # Generate final summary
            summary = {
                "task": state["current_task"],
                "market_data": state.get("market_data", {}),
                "risk_assessment": state.get("risk_assessment", {}),
                "analysis_results": state.get("analysis_results", {}),
                "total_agents_executed": state["iteration_count"],
                "completion_time": datetime.now().isoformat()
            }
            
            state["messages"].append(
                SystemMessage(content=f"Analysis completed. Summary: {json.dumps(summary, indent=2)}")
            )
        
        return state
    
    async def analyze_crypto(self, task: str, config: Optional[Dict] = None) -> Dict[str, Any]:
        """Main entry point for crypto analysis"""
        logger.info(f"🎯 Starting crypto analysis: {task}")
        
        # Initialize state
        initial_state = AgentState(
            messages=[HumanMessage(content=task)],
            current_task=task,
            market_data={},
            analysis_results={},
            risk_assessment={},
            next_agent="data_ingestion",
            iteration_count=0
        )
        
        # Run the workflow
        config = config or {"configurable": {"thread_id": "crypto_analysis_1"}}
        
        try:
            final_state = await self.graph.ainvoke(initial_state, config)
            
            logger.info("🎉 Crypto analysis completed successfully")
            
            return {
                "status": "success",
                "task": task,
                "market_data": final_state.get("market_data", {}),
                "risk_assessment": final_state.get("risk_assessment", {}),
                "analysis_results": final_state.get("analysis_results", {}),
                "messages": [msg.content for msg in final_state["messages"]],
                "completion_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Crypto analysis failed: {e}")
            return {
                "status": "error",
                "task": task,
                "error": str(e),
                "completion_time": datetime.now().isoformat()
            }

# Test function
async def test_agent_system():
    """Test the agent system"""
    print("🚀 Testing QuantCrypto Multi-Agent System...")
    
    agent_system = CryptoAgentSystem()
    
    # Test analysis
    result = await agent_system.analyze_crypto(
        "Analyze Bitcoin and Ethereum for potential rug pull risks and provide 7-day price forecast"
    )
    
    print("\n📊 Analysis Results:")
    print(json.dumps(result, indent=2))
    
    return result

if __name__ == "__main__":
    asyncio.run(test_agent_system())
