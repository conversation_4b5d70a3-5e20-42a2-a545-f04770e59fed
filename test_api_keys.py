#!/usr/bin/env python3
"""
QuantCrypto API Keys Testing & Validation
Tests all configured API keys and validates data access
"""

import os
import requests
import json
import time
from datetime import datetime
from typing import Dict, Any, Tuple
import asyncio
import aiohttp
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class APITester:
    def __init__(self):
        self.results = {}
        self.session = None
        
    async def create_session(self):
        """Create aiohttp session for async requests"""
        self.session = aiohttp.ClientSession()
    
    async def close_session(self):
        """Close aiohttp session"""
        if self.session:
            await self.session.close()
    
    def log_result(self, api_name: str, status: str, message: str, data: Any = None):
        """Log test result"""
        self.results[api_name] = {
            'status': status,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data_sample': data
        }
        
        # Color coding for terminal output
        colors = {
            'SUCCESS': '\033[92m',  # Green
            'ERROR': '\033[91m',    # Red
            'WARNING': '\033[93m',  # Yellow
            'INFO': '\033[94m',     # Blue
            'END': '\033[0m'        # Reset
        }
        
        color = colors.get(status, colors['INFO'])
        print(f"{color}[{status}]{colors['END']} {api_name}: {message}")
    
    async def test_coingecko_api(self):
        """Test CoinGecko API"""
        api_key = os.getenv('COINGECKO_API_KEY')
        if not api_key:
            self.log_result('CoinGecko', 'ERROR', 'API key not found in environment')
            return
        
        try:
            url = f"https://api.coingecko.com/api/v3/simple/price?ids=bitcoin,ethereum&vs_currencies=usd&x_cg_demo_api_key={api_key}"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    self.log_result('CoinGecko', 'SUCCESS', 
                                  f'API working. BTC: ${data.get("bitcoin", {}).get("usd", "N/A")}', 
                                  data)
                else:
                    self.log_result('CoinGecko', 'ERROR', 
                                  f'HTTP {response.status}: {await response.text()}')
        except Exception as e:
            self.log_result('CoinGecko', 'ERROR', f'Request failed: {str(e)}')
    
    async def test_etherscan_api(self):
        """Test Etherscan API"""
        api_key = os.getenv('ETHERSCAN_API_KEY')
        if not api_key:
            self.log_result('Etherscan', 'ERROR', 'API key not found in environment')
            return
        
        try:
            # Test with latest block number
            url = f"https://api.etherscan.io/api?module=proxy&action=eth_blockNumber&apikey={api_key}"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'result' in data:
                        block_num = int(data['result'], 16)
                        self.log_result('Etherscan', 'SUCCESS', 
                                      f'API working. Latest block: {block_num}', 
                                      {'latest_block': block_num})
                    else:
                        self.log_result('Etherscan', 'ERROR', f'Unexpected response: {data}')
                else:
                    self.log_result('Etherscan', 'ERROR', 
                                  f'HTTP {response.status}: {await response.text()}')
        except Exception as e:
            self.log_result('Etherscan', 'ERROR', f'Request failed: {str(e)}')
    
    async def test_dune_api(self):
        """Test Dune Analytics API"""
        api_key = os.getenv('DUNE_API_KEY')
        if not api_key:
            self.log_result('Dune Analytics', 'ERROR', 'API key not found in environment')
            return
        
        try:
            # Test with a simple query to get user info
            url = "https://api.dune.com/api/v1/user"
            headers = {'X-Dune-API-Key': api_key}
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    self.log_result('Dune Analytics', 'SUCCESS', 
                                  'API working. User authenticated', 
                                  {'user_id': data.get('id', 'N/A')})
                else:
                    self.log_result('Dune Analytics', 'ERROR', 
                                  f'HTTP {response.status}: {await response.text()}')
        except Exception as e:
            self.log_result('Dune Analytics', 'ERROR', f'Request failed: {str(e)}')
    
    async def test_coinapi(self):
        """Test CoinAPI"""
        api_key = os.getenv('COIN_API_KEY')
        if not api_key:
            self.log_result('CoinAPI', 'ERROR', 'API key not found in environment')
            return
        
        try:
            url = "https://rest.coinapi.io/v1/exchanges"
            headers = {'X-CoinAPI-Key': api_key}
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    self.log_result('CoinAPI', 'SUCCESS', 
                                  f'API working. Found {len(data)} exchanges', 
                                  {'exchange_count': len(data)})
                else:
                    self.log_result('CoinAPI', 'ERROR', 
                                  f'HTTP {response.status}: {await response.text()}')
        except Exception as e:
            self.log_result('CoinAPI', 'ERROR', f'Request failed: {str(e)}')
    
    async def test_infura_api(self):
        """Test Infura API"""
        api_key = os.getenv('INFURA_API_KEY')
        if not api_key:
            self.log_result('Infura', 'ERROR', 'API key not found in environment')
            return
        
        try:
            # Test Ethereum mainnet
            url = f"https://mainnet.infura.io/v3/{api_key}"
            payload = {
                "jsonrpc": "2.0",
                "method": "eth_blockNumber",
                "params": [],
                "id": 1
            }
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'result' in data:
                        block_num = int(data['result'], 16)
                        self.log_result('Infura', 'SUCCESS', 
                                      f'API working. Latest block: {block_num}', 
                                      {'latest_block': block_num})
                    else:
                        self.log_result('Infura', 'ERROR', f'Unexpected response: {data}')
                else:
                    self.log_result('Infura', 'ERROR', 
                                  f'HTTP {response.status}: {await response.text()}')
        except Exception as e:
            self.log_result('Infura', 'ERROR', f'Request failed: {str(e)}')
    
    async def test_web3_provider(self):
        """Test Web3 Provider URL"""
        provider_url = os.getenv('WEB3_PROVIDER_URL')
        if not provider_url:
            self.log_result('Web3 Provider', 'ERROR', 'Provider URL not found in environment')
            return
        
        try:
            payload = {
                "jsonrpc": "2.0",
                "method": "eth_blockNumber",
                "params": [],
                "id": 1
            }
            
            async with self.session.post(provider_url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'result' in data:
                        block_num = int(data['result'], 16)
                        self.log_result('Web3 Provider', 'SUCCESS', 
                                      f'Provider working. Latest block: {block_num}', 
                                      {'latest_block': block_num})
                    else:
                        self.log_result('Web3 Provider', 'ERROR', f'Unexpected response: {data}')
                else:
                    self.log_result('Web3 Provider', 'ERROR', 
                                  f'HTTP {response.status}: {await response.text()}')
        except Exception as e:
            self.log_result('Web3 Provider', 'ERROR', f'Request failed: {str(e)}')
    
    async def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting QuantCrypto API Keys Validation...")
        print("=" * 60)
        
        await self.create_session()
        
        # Run all tests concurrently
        await asyncio.gather(
            self.test_coingecko_api(),
            self.test_etherscan_api(),
            self.test_dune_api(),
            self.test_coinapi(),
            self.test_infura_api(),
            self.test_web3_provider(),
            return_exceptions=True
        )
        
        await self.close_session()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 API VALIDATION SUMMARY")
        print("=" * 60)
        
        success_count = sum(1 for r in self.results.values() if r['status'] == 'SUCCESS')
        total_count = len(self.results)
        
        for api_name, result in self.results.items():
            status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
            print(f"{status_icon} {api_name}: {result['message']}")
        
        print(f"\n🎯 Success Rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        # Save detailed results
        with open('api_test_results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"📄 Detailed results saved to: api_test_results.json")
        
        return self.results

async def main():
    """Main function"""
    tester = APITester()
    results = await tester.run_all_tests()
    
    # Check if we have enough working APIs for the platform
    working_apis = [name for name, result in results.items() if result['status'] == 'SUCCESS']
    
    if len(working_apis) >= 4:
        print(f"\n🎉 Excellent! {len(working_apis)} APIs are working. Ready for Phase 1.4!")
    elif len(working_apis) >= 2:
        print(f"\n⚠️  {len(working_apis)} APIs working. Sufficient for basic functionality.")
    else:
        print(f"\n🚨 Only {len(working_apis)} APIs working. May need to fix API keys.")

if __name__ == "__main__":
    asyncio.run(main())
