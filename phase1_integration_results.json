{"PostgreSQL Connection": {"status": "PASS", "message": "Database connection successful", "timestamp": "2025-07-06T19:44:04.781836", "data": null}, "Redis Connection": {"status": "FAIL", "message": "Redis connection failed: AUTH <password> called without any password configured for the default user. Are you sure your configuration is correct?", "timestamp": "2025-07-06T19:44:04.822043", "data": null}, "Weaviate Connection": {"status": "FAIL", "message": "Weaviate returned status 404", "timestamp": "2025-07-06T19:44:06.030959", "data": null}, "OLLAMA DeepSeek": {"status": "PASS", "message": "DeepSeek R1 model available", "timestamp": "2025-07-06T19:44:06.051522", "data": null}, "Data Pipeline Init": {"status": "PASS", "message": "Pipeline initialized successfully", "timestamp": "2025-07-06T19:44:06.051627", "data": null}, "CoinGecko API": {"status": "PASS", "message": "BTC price: $108955", "timestamp": "2025-07-06T19:44:06.326974", "data": null}, "Etherscan API": {"status": "PASS", "message": "Latest block: 22863814", "timestamp": "2025-07-06T19:44:06.687938", "data": null}, "Agent System Init": {"status": "PASS", "message": "Agent system initialized successfully", "timestamp": "2025-07-06T19:44:06.741282", "data": null}, "Agent Analysis": {"status": "FAIL", "message": "Analysis timed out after 60 seconds", "timestamp": "2025-07-06T19:45:06.743633", "data": null}, "Data Retrieval": {"status": "PASS", "message": "Retrieved BTC data: $109115.0", "timestamp": "2025-07-06T19:45:06.793942", "data": null}, "Rug Pull Analysis": {"status": "FAIL", "message": "Failed to generate rug pull indicators", "timestamp": "2025-07-06T19:45:06.793963", "data": null}, "Integration Workflow": {"status": "PASS", "message": "All integration tests completed", "timestamp": "2025-07-06T19:45:06.793968", "data": null}}