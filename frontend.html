<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuantCrypto | Institutional-Grade Analytics</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        neumorph: {
                            light: '#e0e0e0',
                            dark: '#bebebe',
                            accent: '#4a6cf7'
                        }
                    },
                    boxShadow: {
                        'neumorph-outer': '8px 8px 16px #bebebe, -8px -8px 16px #ffffff',
                        'neumorph-inner': 'inset 4px 4px 8px #bebebe, inset -4px -4px 8px #ffffff',
                        'neumorph-pressed': 'inset 6px 6px 12px #bebebe, inset -6px -6px 12px #ffffff'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            background-color: #e0e0e0;
            font-family: 'Inter', sans-serif;
            color: #4a4a4a;
            overflow-x: hidden;
        }
        
        .neumorphic-card {
            background: #e0e0e0;
            border-radius: 20px;
            box-shadow: 8px 8px 16px #bebebe, -8px -8px 16px #ffffff;
            transition: all 0.3s ease;
        }
        
        .neumorphic-btn {
            background: #e0e0e0;
            border-radius: 12px;
            box-shadow: 4px 4px 8px #bebebe, -4px -4px 8px #ffffff;
            transition: all 0.2s ease;
        }
        
        .neumorphic-btn:active {
            box-shadow: inset 4px 4px 8px #bebebe, inset -4px -4px 8px #ffffff;
        }
        
        .neumorphic-input {
            background: #e0e0e0;
            border-radius: 12px;
            box-shadow: inset 4px 4px 8px #bebebe, inset -4px -4px 8px #ffffff;
            border: none;
        }
        
        .neumorphic-input:focus {
            outline: none;
            box-shadow: inset 6px 6px 12px #bebebe, inset -6px -6px 12px #ffffff;
        }
        
        .neumorphic-tab {
            background: #e0e0e0;
            border-radius: 12px;
            box-shadow: 4px 4px 8px #bebebe, -4px -4px 8px #ffffff;
            transition: all 0.3s ease;
        }
        
        .neumorphic-tab.active {
            box-shadow: inset 4px 4px 8px #bebebe, inset -4px -4px 8px #ffffff;
        }
        
        .neumorphic-divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #bebebe, transparent);
            margin: 1.5rem 0;
        }
        
        .alert-critical {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(255, 0, 0, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 0, 0, 0); }
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        .token-card:hover {
            transform: translateY(-5px);
            box-shadow: 12px 12px 24px #bebebe, -12px -12px 24px #ffffff;
        }
        
        .glow {
            text-shadow: 0 0 10px rgba(74, 108, 247, 0.5);
        }
    </style>
</head>
<body class="min-h-screen bg-neumorph-light py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <header class="flex flex-col md:flex-row justify-between items-center mb-10">
            <div class="flex items-center mb-6 md:mb-0">
                <div class="neumorphic-card w-16 h-16 rounded-2xl flex items-center justify-center mr-4">
                    <i class="fas fa-chart-line text-neumorph-accent text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">Quant<span class="text-neumorph-accent">Crypto</span></h1>
                    <p class="text-gray-600">Institutional-Grade Crypto Analytics</p>
                </div>
            </div>
            
            <div class="flex space-x-4">
                <div class="neumorphic-card px-4 py-2 flex items-center">
                    <i class="fas fa-wallet text-neumorph-accent mr-2"></i>
                    <span>$4,327,891.24</span>
                </div>
                <div class="neumorphic-card px-4 py-2 flex items-center">
                    <i class="fas fa-user text-neumorph-accent mr-2"></i>
                    <span>quant_dev_01</span>
                </div>
            </div>
        </header>
        
        <!-- Main Dashboard -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Market Overview -->
            <div class="neumorphic-card p-6 lg:col-span-2">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">Market Overview</h2>
                    <div class="flex space-x-2">
                        <button class="neumorphic-btn px-3 py-1 text-sm">24H</button>
                        <button class="neumorphic-btn px-3 py-1 text-sm bg-neumorph-accent text-white">7D</button>
                        <button class="neumorphic-btn px-3 py-1 text-sm">1M</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="marketChart"></canvas>
                </div>
            </div>
            
            <!-- Rug Detection Alerts -->
            <div class="neumorphic-card p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">Rug Detection</h2>
                    <span class="neumorphic-btn px-3 py-1 text-sm flex items-center">
                        <i class="fas fa-sync-alt mr-2"></i> Real-time
                    </span>
                </div>
                
                <div class="space-y-4">
                    <div class="neumorphic-card p-4 alert-critical">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="font-bold text-red-600">HIGH RISK</h3>
                                <p class="text-sm">MoonDoge (MDOGE)</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm">-87.4% in 2H</p>
                                <p class="text-xs text-gray-500">New token</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="neumorphic-card p-4">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="font-bold text-yellow-600">MEDIUM RISK</h3>
                                <p class="text-sm">EtherMax (EMAX)</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm">-42.1% in 24H</p>
                                <p class="text-xs text-gray-500">High volatility</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="neumorphic-card p-4">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="font-bold text-green-600">LOW RISK</h3>
                                <p class="text-sm">PolyPup (PUP)</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm">+12.3% in 24H</p>
                                <p class="text-xs text-gray-500">Stable pattern</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="neumorphic-btn w-full mt-6 py-3 font-medium flex items-center justify-center">
                    <i class="fas fa-robot mr-2"></i> Run Deep Analysis
                </button>
            </div>
        </div>
        
        <!-- Token Analytics -->
        <div class="neumorphic-card p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-gray-800">New Token Analytics</h2>
                <div class="flex space-x-2">
                    <div class="neumorphic-input px-4 py-2">
                        <i class="fas fa-search mr-2 text-gray-500"></i>
                        <input type="text" placeholder="Search tokens..." class="bg-transparent focus:outline-none">
                    </div>
                    <button class="neumorphic-btn px-4 py-2">
                        <i class="fas fa-sliders-h"></i>
                    </button>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Token Card 1 -->
                <div class="neumorphic-card p-4 token-card transition-all duration-300">
                    <div class="flex justify-between mb-3">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-indigo-600 flex items-center justify-center">
                            <span class="text-white font-bold">S</span>
                        </div>
                        <span class="neumorphic-btn px-2 py-1 text-xs">+24.7%</span>
                    </div>
                    <h3 class="font-bold text-lg mb-1">Solimatic (SOLI)</h3>
                    <p class="text-sm text-gray-600 mb-3">Solana Ecosystem</p>
                    <div class="h-24 mb-3">
                        <canvas id="tokenChart1"></canvas>
                    </div>
                    <div class="flex justify-between text-xs">
                        <span>Liquidity: $2.1M</span>
                        <span>Age: 3D</span>
                    </div>
                </div>
                
                <!-- Token Card 2 -->
                <div class="neumorphic-card p-4 token-card transition-all duration-300">
                    <div class="flex justify-between mb-3">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-yellow-400 to-red-500 flex items-center justify-center">
                            <span class="text-white font-bold">A</span>
                        </div>
                        <span class="neumorphic-btn px-2 py-1 text-xs bg-red-100 text-red-800">-8.3%</span>
                    </div>
                    <h3 class="font-bold text-lg mb-1">ApeRocket (ROCK)</h3>
                    <p class="text-sm text-gray-600 mb-3">BNB Chain</p>
                    <div class="h-24 mb-3">
                        <canvas id="tokenChart2"></canvas>
                    </div>
                    <div class="flex justify-between text-xs">
                        <span>Liquidity: $1.4M</span>
                        <span>Age: 1D</span>
                    </div>
                </div>
                
                <!-- Token Card 3 -->
                <div class="neumorphic-card p-4 token-card transition-all duration-300">
                    <div class="flex justify-between mb-3">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-green-400 to-teal-600 flex items-center justify-center">
                            <span class="text-white font-bold">P</span>
                        </div>
                        <span class="neumorphic-btn px-2 py-1 text-xs">+56.1%</span>
                    </div>
                    <h3 class="font-bold text-lg mb-1">PolyPup (PUP)</h3>
                    <p class="text-sm text-gray-600 mb-3">Polygon Ecosystem</p>
                    <div class="h-24 mb-3">
                        <canvas id="tokenChart3"></canvas>
                    </div>
                    <div class="flex justify-between text-xs">
                        <span>Liquidity: $3.7M</span>
                        <span>Age: 5D</span>
                    </div>
                </div>
                
                <!-- Token Card 4 -->
                <div class="neumorphic-card p-4 token-card transition-all duration-300">
                    <div class="flex justify-between mb-3">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-purple-400 to-pink-600 flex items-center justify-center">
                            <span class="text-white font-bold">M</span>
                        </div>
                        <span class="neumorphic-btn px-2 py-1 text-xs">+3.2%</span>
                    </div>
                    <h3 class="font-bold text-lg mb-1">MetaDoge (MDOGE)</h3>
                    <p class="text-sm text-gray-600 mb-3">Ethereum</p>
                    <div class="h-24 mb-3">
                        <canvas id="tokenChart4"></canvas>
                    </div>
                    <div class="flex justify-between text-xs">
                        <span>Liquidity: $5.8M</span>
                        <span>Age: 2D</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Forecasting Section -->
        <div class="neumorphic-card p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-gray-800">Quantitative Forecasting</h2>
                <div class="flex space-x-3">
                    <button class="neumorphic-btn px-4 py-2 flex items-center">
                        <i class="fas fa-brain mr-2"></i> AI Predictions
                    </button>
                    <button class="neumorphic-btn px-4 py-2 flex items-center bg-neumorph-accent text-white">
                        <i class="fas fa-bolt mr-2"></i> Run Simulation
                    </button>
                </div>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="chart-container">
                    <canvas id="forecastChart"></canvas>
                </div>
                <div>
                    <div class="neumorphic-card p-4 mb-6">
                        <h3 class="font-bold mb-3">Forecast Summary</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span>BTC 7D Forecast:</span>
                                <span class="font-bold text-green-600">+12.4%</span>
                            </div>
                            <div class="flex justify-between">
                                <span>ETH 7D Forecast:</span>
                                <span class="font-bold text-green-600">+8.7%</span>
                            </div>
                            <div class="flex justify-between">
                                <span>SOL 7D Forecast:</span>
                                <span class="font-bold text-red-600">-3.2%</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Market Sentiment:</span>
                                <span class="font-bold text-green-600">Bullish (78%)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="neumorphic-card p-4">
                        <h3 class="font-bold mb-3">Agent System Status</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                <span>Data Ingestion Agent</span>
                                <span class="ml-auto text-xs">Processing 24.5k txn/s</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                <span>Pattern Recognition Agent</span>
                                <span class="ml-auto text-xs">98.7% accuracy</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                <span>Risk Assessment Agent</span>
                                <span class="ml-auto text-xs">Monitoring 1.2k tokens</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                                <span>Forecasting Agent</span>
                                <span class="ml-auto text-xs">Recalibrating models</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <footer class="text-center text-gray-600 text-sm mt-8">
            <p class="mb-2">QuantCrypto Analytics Platform v2.4.1 | Institutional Use Only</p>
            <p>AI-Powered Forecasting • Real-time Rug Detection • Multi-Agent System</p>
        </footer>
    </div>

    <script>
        // Initialize Charts
        document.addEventListener('DOMContentLoaded', function() {
            // Market Overview Chart
            const marketCtx = document.getElementById('marketChart').getContext('2d');
            new Chart(marketCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                    datasets: [
                        {
                            label: 'BTC',
                            data: [35000, 42000, 38000, 45000, 48000, 52000, 57000],
                            borderColor: '#4a6cf7',
                            backgroundColor: 'rgba(74, 108, 247, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: 'ETH',
                            data: [2500, 2800, 3100, 2900, 3200, 3500, 3800],
                            borderColor: '#8e6cf7',
                            backgroundColor: 'rgba(142, 108, 247, 0.1)',
                            tension: 0.3,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            grid: {
                                color: 'rgba(190, 190, 190, 0.2)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(190, 190, 190, 0.2)'
                            }
                        }
                    }
                }
            });
            
            // Token Charts
            const tokenData = [
                { id: 'tokenChart1', data: [0.12, 0.18, 0.15, 0.22, 0.25, 0.30, 0.35] },
                { id: 'tokenChart2', data: [0.25, 0.30, 0.28, 0.22, 0.20, 0.18, 0.15] },
                { id: 'tokenChart3', data: [0.08, 0.10, 0.15, 0.20, 0.25, 0.35, 0.45] },
                { id: 'tokenChart4', data: [0.15, 0.14, 0.16, 0.15, 0.16, 0.17, 0.18] }
            ];
            
            tokenData.forEach(token => {
                const ctx = document.getElementById(token.id).getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1H', '3H', '6H', '12H', '18H', '24H', '36H'],
                        datasets: [{
                            data: token.data,
                            borderColor: '#4a6cf7',
                            backgroundColor: 'rgba(74, 108, 247, 0.1)',
                            tension: 0.4,
                            fill: true,
                            pointRadius: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                display: false
                            },
                            x: {
                                display: false
                            }
                        }
                    }
                });
            });
            
            // Forecast Chart
            const forecastCtx = document.getElementById('forecastChart').getContext('2d');
            new Chart(forecastCtx, {
                type: 'line',
                data: {
                    labels: ['Now', '+6H', '+12H', '+18H', '+1D', '+2D', '+3D', '+4D', '+5D', '+6D', '+7D'],
                    datasets: [
                        {
                            label: 'Price Forecast',
                            data: [57000, 57500, 58000, 59000, 60000, 61000, 62500, 64000, 65500, 67000, 68500],
                            borderColor: '#4a6cf7',
                            backgroundColor: 'rgba(74, 108, 247, 0.1)',
                            tension: 0.2,
                            fill: true
                        },
                        {
                            label: 'Confidence Interval',
                            data: [56500, 57000, 57500, 58000, 59000, 60000, 61000, 62000, 63000, 64000, 65000],
                            borderColor: 'rgba(74, 108, 247, 0.3)',
                            backgroundColor: 'rgba(74, 108, 247, 0.05)',
                            borderDash: [5, 5],
                            tension: 0.2,
                            fill: true,
                            pointRadius: 0
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            grid: {
                                color: 'rgba(190, 190, 190, 0.2)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(190, 190, 190, 0.2)'
                            }
                        }
                    }
                }
            });
            
            // Simulate real-time updates
            setInterval(() => {
                const alerts = document.querySelectorAll('.neumorphic-card:not(.alert-critical)');
                if(alerts.length > 0) {
                    const randomAlert = alerts[Math.floor(Math.random() * alerts.length)];
                    randomAlert.classList.add('alert-critical');
                    
                    setTimeout(() => {
                        randomAlert.classList.remove('alert-critical');
                    }, 3000);
                }
            }, 8000);
        });
    </script>
</body>
</html>