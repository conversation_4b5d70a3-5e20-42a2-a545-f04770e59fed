#!/usr/bin/env python3
"""
QuantCrypto Phase 1 Integration Testing
Comprehensive testing of all Phase 1 components
"""

import os
import sys
import asyncio
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any

# Add src to path
sys.path.append('src')

from agents.crypto_agent_system import CryptoAgentSystem
from pipelines.crypto_data_pipeline import CryptoDataPipeline

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase1IntegrationTester:
    """Integration tester for Phase 1 components"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        
    def log_test_result(self, test_name: str, status: str, message: str, data: Any = None):
        """Log test result"""
        self.test_results[test_name] = {
            'status': status,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        
        # Color coding
        colors = {
            'PASS': '\033[92m',    # Green
            'FAIL': '\033[91m',    # Red
            'SKIP': '\033[93m',    # Yellow
            'INFO': '\033[94m',    # Blue
            'END': '\033[0m'       # Reset
        }
        
        color = colors.get(status, colors['INFO'])
        print(f"{color}[{status}]{colors['END']} {test_name}: {message}")
    
    async def test_infrastructure_services(self):
        """Test local infrastructure services"""
        logger.info("🔧 Testing infrastructure services...")
        
        # Test PostgreSQL
        try:
            import psycopg2
            conn = psycopg2.connect(
                host="localhost",
                database="quantum_market_db",
                user="quantum_user",
                password="6UD4nu33sGV3YGUfXRxNrq6mr"
            )
            conn.close()
            self.log_test_result("PostgreSQL Connection", "PASS", "Database connection successful")
        except Exception as e:
            self.log_test_result("PostgreSQL Connection", "FAIL", f"Database connection failed: {e}")
        
        # Test Redis
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, password='OigGCgnXdZJOy62gNxnkUWR6P', decode_responses=True)
            r.ping()
            self.log_test_result("Redis Connection", "PASS", "Redis connection successful")
        except Exception as e:
            self.log_test_result("Redis Connection", "FAIL", f"Redis connection failed: {e}")
        
        # Test Weaviate
        try:
            import requests
            response = requests.get("http://localhost:8080/v1/meta", timeout=5)
            if response.status_code == 200:
                self.log_test_result("Weaviate Connection", "PASS", "Weaviate connection successful")
            else:
                self.log_test_result("Weaviate Connection", "FAIL", f"Weaviate returned status {response.status_code}")
        except Exception as e:
            self.log_test_result("Weaviate Connection", "FAIL", f"Weaviate connection failed: {e}")
        
        # Test OLLAMA
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json()
                deepseek_available = any('deepseek-r1' in model['name'] for model in models.get('models', []))
                if deepseek_available:
                    self.log_test_result("OLLAMA DeepSeek", "PASS", "DeepSeek R1 model available")
                else:
                    self.log_test_result("OLLAMA DeepSeek", "FAIL", "DeepSeek R1 model not found")
            else:
                self.log_test_result("OLLAMA Connection", "FAIL", f"OLLAMA returned status {response.status_code}")
        except Exception as e:
            self.log_test_result("OLLAMA Connection", "FAIL", f"OLLAMA connection failed: {e}")
    
    async def test_data_pipeline(self):
        """Test data pipeline functionality"""
        logger.info("📊 Testing data pipeline...")
        
        try:
            pipeline = CryptoDataPipeline()
            
            # Test pipeline initialization
            self.log_test_result("Data Pipeline Init", "PASS", "Pipeline initialized successfully")
            
            # Test API connections (short test)
            pipeline.session = __import__('aiohttp').ClientSession()
            
            # Test CoinGecko API
            try:
                url = f"https://api.coingecko.com/api/v3/simple/price"
                params = {
                    'ids': 'bitcoin',
                    'vs_currencies': 'usd',
                    'x_cg_demo_api_key': pipeline.api_keys['coingecko']
                }
                
                async with pipeline.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        btc_price = data.get('bitcoin', {}).get('usd', 0)
                        self.log_test_result("CoinGecko API", "PASS", f"BTC price: ${btc_price}")
                    else:
                        self.log_test_result("CoinGecko API", "FAIL", f"HTTP {response.status}")
            except Exception as e:
                self.log_test_result("CoinGecko API", "FAIL", f"API test failed: {e}")
            
            # Test Etherscan API
            try:
                url = "https://api.etherscan.io/api"
                params = {
                    'module': 'proxy',
                    'action': 'eth_blockNumber',
                    'apikey': pipeline.api_keys['etherscan']
                }
                
                async with pipeline.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'result' in data:
                            block_num = int(data['result'], 16)
                            self.log_test_result("Etherscan API", "PASS", f"Latest block: {block_num}")
                        else:
                            self.log_test_result("Etherscan API", "FAIL", "Invalid response format")
                    else:
                        self.log_test_result("Etherscan API", "FAIL", f"HTTP {response.status}")
            except Exception as e:
                self.log_test_result("Etherscan API", "FAIL", f"API test failed: {e}")
            
            await pipeline.session.close()
            
        except Exception as e:
            self.log_test_result("Data Pipeline Test", "FAIL", f"Pipeline test failed: {e}")
    
    async def test_agent_system(self):
        """Test LangGraph agent system"""
        logger.info("🤖 Testing agent system...")
        
        try:
            agent_system = CryptoAgentSystem()
            self.log_test_result("Agent System Init", "PASS", "Agent system initialized successfully")
            
            # Test simple analysis (with timeout)
            try:
                result = await asyncio.wait_for(
                    agent_system.analyze_crypto("Test Bitcoin analysis for integration testing"),
                    timeout=60  # 1 minute timeout
                )
                
                if result['status'] == 'success':
                    self.log_test_result("Agent Analysis", "PASS", "Analysis completed successfully", 
                                       {'market_data_keys': list(result.get('market_data', {}).keys())})
                else:
                    self.log_test_result("Agent Analysis", "FAIL", f"Analysis failed: {result.get('error', 'Unknown error')}")
                    
            except asyncio.TimeoutError:
                self.log_test_result("Agent Analysis", "FAIL", "Analysis timed out after 60 seconds")
            except Exception as e:
                self.log_test_result("Agent Analysis", "FAIL", f"Analysis error: {e}")
                
        except Exception as e:
            self.log_test_result("Agent System Test", "FAIL", f"Agent system test failed: {e}")
    
    async def test_integration_workflow(self):
        """Test integrated workflow"""
        logger.info("🔄 Testing integrated workflow...")
        
        try:
            # Initialize components
            pipeline = CryptoDataPipeline()
            agent_system = CryptoAgentSystem()
            
            # Simulate data collection
            pipeline.market_data_cache = {
                'BTC': {
                    'symbol': 'BTC',
                    'price': 109115.0,
                    'change_24h': 2.3,
                    'volume_24h': 28500000000.0,
                    'market_cap': 2100000000000.0,
                    'timestamp': datetime.now().isoformat(),
                    'source': 'test'
                }
            }
            
            # Test data retrieval
            btc_data = pipeline.get_market_data('BTC')
            if btc_data:
                self.log_test_result("Data Retrieval", "PASS", f"Retrieved BTC data: ${btc_data['price']}")
            else:
                self.log_test_result("Data Retrieval", "FAIL", "Failed to retrieve market data")
            
            # Test rug pull analysis
            rug_indicators = pipeline.get_rug_pull_indicators('******************************************')
            if rug_indicators and 'overall_risk_score' in rug_indicators:
                self.log_test_result("Rug Pull Analysis", "PASS", 
                                   f"Risk score: {rug_indicators['overall_risk_score']}/100")
            else:
                self.log_test_result("Rug Pull Analysis", "FAIL", "Failed to generate rug pull indicators")
            
            self.log_test_result("Integration Workflow", "PASS", "All integration tests completed")
            
        except Exception as e:
            self.log_test_result("Integration Workflow", "FAIL", f"Integration test failed: {e}")
    
    async def run_all_tests(self):
        """Run all integration tests"""
        self.start_time = time.time()
        
        print("🚀 Starting QuantCrypto Phase 1 Integration Testing...")
        print("=" * 70)
        
        # Run test suites
        await self.test_infrastructure_services()
        print()
        
        await self.test_data_pipeline()
        print()
        
        await self.test_agent_system()
        print()
        
        await self.test_integration_workflow()
        print()
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate test summary"""
        total_time = time.time() - self.start_time
        
        print("=" * 70)
        print("📊 PHASE 1 INTEGRATION TEST SUMMARY")
        print("=" * 70)
        
        passed = sum(1 for r in self.test_results.values() if r['status'] == 'PASS')
        failed = sum(1 for r in self.test_results.values() if r['status'] == 'FAIL')
        total = len(self.test_results)
        
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'PASS' else "❌"
            print(f"{status_icon} {test_name}: {result['message']}")
        
        print(f"\n🎯 Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        print(f"⏱️  Total time: {total_time:.2f} seconds")
        
        if passed >= total * 0.8:  # 80% pass rate
            print("🎉 Phase 1 integration tests PASSED! Ready for Phase 2.")
        else:
            print("⚠️  Phase 1 integration tests need attention before Phase 2.")
        
        # Save detailed results
        with open('phase1_integration_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"📄 Detailed results saved to: phase1_integration_results.json")

async def main():
    """Main test function"""
    tester = Phase1IntegrationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
