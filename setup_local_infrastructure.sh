#!/bin/bash

# QuantCrypto Local Infrastructure Setup for M1 Max
# Phase 1.1: Install and configure PostgreSQL, <PERSON>is, Kafka, Weaviate locally

set -e

echo "🚀 Starting QuantCrypto Local Infrastructure Setup for M1 Max..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on M1 Mac
if [[ $(uname -m) != "arm64" ]]; then
    print_error "This script is optimized for M1 Mac (arm64). Current architecture: $(uname -m)"
    exit 1
fi

print_status "Detected M1 Mac with $(sysctl -n hw.memsize | awk '{print $1/1024/1024/1024}')GB RAM"

# Update Homebrew
print_status "Updating Homebrew..."
brew update

# Install PostgreSQL 15 (optimized for M1)
print_status "Installing PostgreSQL 15..."
brew install postgresql@15
brew services start postgresql@15

# Install Redis 7 (with M1 optimizations)
print_status "Installing Redis 7..."
brew install redis
brew services start redis

# Install Apache Kafka (with Zookeeper)
print_status "Installing Apache Kafka..."
brew install kafka
brew services start zookeeper
brew services start kafka

# Install Weaviate locally
print_status "Installing Weaviate locally..."
brew install docker
# Start Docker if not running
if ! docker info > /dev/null 2>&1; then
    print_warning "Docker is not running. Please start Docker Desktop and run this script again."
    exit 1
fi

# Install Python 3.11 (optimal for M1 performance)
print_status "Installing Python 3.11..."
brew install python@3.11
pip3.11 install --upgrade pip

# Install Node.js 20 LTS (for potential frontend integration)
print_status "Installing Node.js 20 LTS..."
brew install node@20

# Create project directories
print_status "Creating project directory structure..."
mkdir -p data/{postgres,redis,kafka,weaviate}
mkdir -p logs
mkdir -p config
mkdir -p src/{agents,models,pipelines,utils}
mkdir -p tests

# Set up PostgreSQL database
print_status "Setting up PostgreSQL database..."
createdb quantum_market_db 2>/dev/null || print_warning "Database quantum_market_db already exists"

# Create PostgreSQL user
psql postgres -c "CREATE USER quantum_user WITH PASSWORD '6UD4nu33sGV3YGUfXRxNrq6mr';" 2>/dev/null || print_warning "User quantum_user already exists"
psql postgres -c "GRANT ALL PRIVILEGES ON DATABASE quantum_market_db TO quantum_user;" 2>/dev/null || true

# Configure Redis
print_status "Configuring Redis..."
redis-cli CONFIG SET requirepass "OigGCgnXdZJOy62gNxnkUWR6P"
redis-cli CONFIG REWRITE

# Test services
print_status "Testing service connections..."

# Test PostgreSQL
if psql -h localhost -U quantum_user -d quantum_market_db -c "SELECT version();" > /dev/null 2>&1; then
    print_status "✅ PostgreSQL connection successful"
else
    print_error "❌ PostgreSQL connection failed"
fi

# Test Redis
if redis-cli -a "OigGCgnXdZJOy62gNxnkUWR6P" ping | grep -q "PONG"; then
    print_status "✅ Redis connection successful"
else
    print_error "❌ Redis connection failed"
fi

# Test Kafka
if kafka-topics --bootstrap-server localhost:9092 --list > /dev/null 2>&1; then
    print_status "✅ Kafka connection successful"
else
    print_error "❌ Kafka connection failed"
fi

print_status "🎉 Local infrastructure setup completed!"
print_status "Next: Run setup_weaviate.sh to configure vector database"
