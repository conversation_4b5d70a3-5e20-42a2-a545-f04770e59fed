#!/bin/bash

# QuantCrypto Weaviate Local Setup for M1 Max
# Optimized vector database configuration for crypto analytics

set -e

echo "🔍 Setting up Weaviate Vector Database locally..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create Weaviate Docker Compose configuration
print_status "Creating Weaviate Docker Compose configuration..."

cat > docker-compose.weaviate.yml << 'EOF'
version: '3.8'
services:
  weaviate:
    command:
      - --host
      - 0.0.0.0
      - --port
      - '8080'
      - --scheme
      - http
    image: cr.weaviate.io/semitechnologies/weaviate:1.26.1
    ports:
      - "8080:8080"
      - "50051:50051"
    volumes:
      - weaviate_data:/var/lib/weaviate
    restart: on-failure:0
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
      # M1 Mac optimizations
      GOMAXPROCS: '8'  # Use 8 performance cores
      GOMEMLIMIT: '16GiB'  # Limit memory usage
    platform: linux/arm64  # M1 Mac compatibility

volumes:
  weaviate_data:
    driver: local
EOF

# Start Weaviate
print_status "Starting Weaviate container..."
docker-compose -f docker-compose.weaviate.yml up -d

# Wait for Weaviate to be ready
print_status "Waiting for Weaviate to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:8080/v1/meta > /dev/null 2>&1; then
        print_status "✅ Weaviate is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "❌ Weaviate failed to start after 30 seconds"
        exit 1
    fi
    sleep 1
done

# Test Weaviate connection
print_status "Testing Weaviate connection..."
WEAVIATE_RESPONSE=$(curl -s http://localhost:8080/v1/meta)
if echo "$WEAVIATE_RESPONSE" | grep -q "version"; then
    print_status "✅ Weaviate connection successful"
    echo "Weaviate Version: $(echo "$WEAVIATE_RESPONSE" | grep -o '"version":"[^"]*' | cut -d'"' -f4)"
else
    print_error "❌ Weaviate connection failed"
    exit 1
fi

print_status "🎉 Weaviate setup completed!"
print_status "Weaviate is running at: http://localhost:8080"
print_status "Next: Install OLLAMA and DeepSeek R1-32B model"
