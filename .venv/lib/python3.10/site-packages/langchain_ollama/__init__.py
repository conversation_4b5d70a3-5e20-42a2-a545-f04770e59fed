"""This is the langchain_ollama package.

It provides infrastructure for interacting with the Ollama service.
"""

from importlib import metadata

from langchain_ollama.chat_models import ChatOllama
from langchain_ollama.embeddings import OllamaEmbeddings
from langchain_ollama.llms import OllamaLLM

try:
    __version__ = metadata.version(__package__)
except metadata.PackageNotFoundError:
    # Case where package metadata is not available.
    __version__ = ""
del metadata  # optional, avoids polluting the results of dir(__package__)

__all__ = [
    "ChatOllama",
    "OllamaLLM",
    "OllamaEmbeddings",
    "__version__",
]
