{"CoinGecko": {"status": "SUCCESS", "message": "API working. BTC: $109115", "timestamp": "2025-07-06T19:25:39.308559", "data_sample": {"bitcoin": {"usd": 109115}, "ethereum": {"usd": 2568.13}}}, "Web3 Provider": {"status": "SUCCESS", "message": "Provider working. Latest block: 22863724", "timestamp": "2025-07-06T19:25:39.332545", "data_sample": {"latest_block": 22863724}}, "Dune Analytics": {"status": "ERROR", "message": "HTTP 404: {\"error\":\"URL not found\"}", "timestamp": "2025-07-06T19:25:39.502520", "data_sample": null}, "Infura": {"status": "SUCCESS", "message": "API working. Latest block: 22863724", "timestamp": "2025-07-06T19:25:39.517233", "data_sample": {"latest_block": 22863724}}, "Etherscan": {"status": "SUCCESS", "message": "API working. Latest block: 22863723", "timestamp": "2025-07-06T19:25:39.584639", "data_sample": {"latest_block": 22863723}}, "CoinAPI": {"status": "ERROR", "message": "HTTP 403: {\n  \"title\": \"Forbidden\",\n  \"status\": 403,\n  \"detail\": \"Quota exceeded: Insufficient Usage Credits or Subscription.\",\n  \"error\": \"Forbidden (Quota exceeded: Insufficient Usage Credits or Subscription.)\",\n  \"QuotaKey\": \"BA\",\n  \"QuotaName\": \"Insufficient Usage Credits or Subscription\",\n  \"QuotaType\": \"Organization Limit\",\n  \"QuotaValueCurrentUsage\": 2510,\n  \"QuotaValue\": 2500,\n  \"QuotaValueUnit\": \"$\",\n  \"QuotaValueAdjustable\": \"Yes, acquire or upgrade subscription, add service credits manually or setup auto-recharge.\"\n}", "timestamp": "2025-07-06T19:25:39.737206", "data_sample": null}}